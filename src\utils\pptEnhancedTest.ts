/**
 * PPT演示模式增强功能测试工具
 * 
 * 用于验证PPT增强功能的各项特性
 */

import { PPTModelLoader } from '@/views/scene/lib/ppt/PPTModelLoader';
import { useGlobalThreeStore, PPTViewBindingUtils } from '@/views/scene/store/globalThreeStore';
import { buildingData } from '@/data/buildingData';

export class PPTEnhancedTester {
  private pptModelLoader: PPTModelLoader;
  private globalThreeStore: any;

  constructor() {
    this.pptModelLoader = PPTModelLoader.getInstance();
    this.globalThreeStore = useGlobalThreeStore();
  }

  /**
   * 测试场景切换性能
   */
  async testSceneSwitchingPerformance(): Promise<void> {
    console.log('🚀 开始测试场景切换性能...');

    // 测试外景切换
    const exteriorStart = performance.now();
    await this.pptModelLoader.switchToExterior();
    const exteriorTime = performance.now() - exteriorStart;
    console.log(`✅ 外景切换耗时: ${Math.round(exteriorTime)}ms`);

    // 测试楼层切换
    const floors = ['1F', '2F', '3F', '4F', '5F', 'BF'];
    const floorTimes: Record<string, number> = {};

    for (const floorId of floors) {
      const floorStart = performance.now();
      try {
        await this.pptModelLoader.switchToFloor(floorId);
        const floorTime = performance.now() - floorStart;
        floorTimes[floorId] = Math.round(floorTime);
        console.log(`✅ ${floorId}切换耗时: ${floorTimes[floorId]}ms`);
      } catch (error) {
        console.error(`❌ ${floorId}切换失败:`, error);
      }
    }

    // 计算平均切换时间
    const avgTime = Object.values(floorTimes).reduce((a, b) => a + b, 0) / Object.values(floorTimes).length;
    console.log(`📊 平均切换时间: ${Math.round(avgTime)}ms`);

    // 性能评估
    if (avgTime < 100) {
      console.log('🎉 性能优秀！切换时间 < 100ms');
    } else if (avgTime < 500) {
      console.log('👍 性能良好！切换时间 < 500ms');
    } else {
      console.log('⚠️ 性能需要优化，切换时间 > 500ms');
    }
  }

  /**
   * 测试视角绑定功能
   */
  async testViewBindingFeatures(): Promise<void> {
    console.log('🎯 开始测试视角绑定功能...');

    // 创建测试绑定数据
    const testBindings = [
      {
        slideIndex: 0,
        position: { x: 10, y: 5, z: 15 },
        target: { x: 0, y: 0, z: 0 },
        sceneType: 'exterior' as const,
        name: '外景全貌',
      },
      {
        slideIndex: 1,
        position: { x: 5, y: 2, z: 8 },
        target: { x: 2, y: 1, z: 3 },
        sceneType: 'interior' as const,
        floorId: '3F',
        name: '3楼机房',
      },
    ];

    // 测试增强绑定创建
    for (const testData of testBindings) {
      try {
        const binding = PPTViewBindingUtils.createEnhancedBinding(
          testData.slideIndex,
          testData.position,
          testData.target,
          {
            name: testData.name,
            sceneType: testData.sceneType,
            floorId: testData.floorId,
            description: `测试绑定 - ${testData.name}`,
          }
        );

        this.globalThreeStore.addPPTViewBinding(binding);
        console.log(`✅ 创建增强绑定成功: ${testData.name}`);
      } catch (error) {
        console.error(`❌ 创建绑定失败: ${testData.name}`, error);
      }
    }

    // 测试绑定应用
    for (const testData of testBindings) {
      try {
        const success = await this.globalThreeStore.applyPPTViewBinding(testData.slideIndex);
        if (success) {
          console.log(`✅ 应用绑定成功: ${testData.name}`);
        } else {
          console.log(`⚠️ 绑定不存在: ${testData.name}`);
        }
      } catch (error) {
        console.error(`❌ 应用绑定失败: ${testData.name}`, error);
      }
    }
  }

  /**
   * 测试格式兼容性
   */
  testFormatCompatibility(): void {
    console.log('🔄 开始测试格式兼容性...');

    // 测试旧格式识别
    const legacyBinding = [10, 5, 15, 0, 0, 0];
    const isLegacy = PPTViewBindingUtils.isLegacyBinding(legacyBinding);
    console.log(`✅ 旧格式识别: ${isLegacy ? '正确' : '错误'}`);

    // 测试格式转换
    try {
      const converted = PPTViewBindingUtils.convertLegacyToEnhanced(legacyBinding, 0);
      const isEnhanced = PPTViewBindingUtils.isEnhancedBinding(converted);
      console.log(`✅ 格式转换: ${isEnhanced ? '成功' : '失败'}`);

      // 测试反向转换
      const backConverted = PPTViewBindingUtils.convertEnhancedToLegacy(converted);
      const isEqual = JSON.stringify(backConverted) === JSON.stringify(legacyBinding);
      console.log(`✅ 反向转换: ${isEqual ? '成功' : '失败'}`);
    } catch (error) {
      console.error('❌ 格式转换失败:', error);
    }
  }

  /**
   * 测试缓存机制
   */
  async testCachingMechanism(): Promise<void> {
    console.log('💾 开始测试缓存机制...');

    // 测试模型缓存获取
    const exteriorPath = buildingData.modelPath;
    const exteriorModel = (this.pptModelLoader as any).getModelFromCache(exteriorPath);
    console.log(`✅ 外景模型缓存: ${exteriorModel ? '存在' : '不存在'}`);

    // 测试楼层模型缓存
    for (const floor of buildingData.floors) {
      const floorModel = (this.pptModelLoader as any).getModelFromCache(floor.modelPath);
      console.log(`✅ ${floor.id}模型缓存: ${floorModel ? '存在' : '不存在'}`);
    }

    // 测试场景信息获取
    const sceneType = this.pptModelLoader.getCurrentSceneType();
    const floorId = this.pptModelLoader.getCurrentFloorId();
    const description = this.pptModelLoader.getSceneDescription();
    
    console.log(`📍 当前场景类型: ${sceneType || '未知'}`);
    console.log(`📍 当前楼层ID: ${floorId || '无'}`);
    console.log(`📍 场景描述: ${description}`);
  }

  /**
   * 运行完整测试套件
   */
  async runFullTestSuite(): Promise<void> {
    console.log('🧪 开始运行PPT增强功能完整测试套件...');
    console.log('='.repeat(50));

    try {
      // 1. 测试格式兼容性
      this.testFormatCompatibility();
      console.log('');

      // 2. 测试缓存机制
      await this.testCachingMechanism();
      console.log('');

      // 3. 测试视角绑定功能
      await this.testViewBindingFeatures();
      console.log('');

      // 4. 测试场景切换性能
      await this.testSceneSwitchingPerformance();
      console.log('');

      console.log('🎉 测试套件运行完成！');
    } catch (error) {
      console.error('❌ 测试套件运行失败:', error);
    }

    console.log('='.repeat(50));
  }

  /**
   * 生成性能报告
   */
  async generatePerformanceReport(): Promise<string> {
    const report = {
      timestamp: new Date().toISOString(),
      tests: {
        sceneSwitching: {},
        viewBinding: {},
        caching: {},
        compatibility: {},
      },
    };

    // 场景切换性能测试
    const exteriorStart = performance.now();
    await this.pptModelLoader.switchToExterior();
    report.tests.sceneSwitching = {
      exteriorSwitchTime: Math.round(performance.now() - exteriorStart),
    };

    // 视角绑定测试
    const bindingStart = performance.now();
    const testBinding = PPTViewBindingUtils.createEnhancedBinding(
      0,
      { x: 0, y: 0, z: 0 },
      { x: 0, y: 0, z: 0 },
      { name: '测试绑定' }
    );
    report.tests.viewBinding = {
      bindingCreationTime: Math.round(performance.now() - bindingStart),
    };

    // 缓存测试
    const cacheStart = performance.now();
    const exteriorModel = (this.pptModelLoader as any).getModelFromCache(buildingData.modelPath);
    report.tests.caching = {
      cacheAccessTime: Math.round(performance.now() - cacheStart),
      modelCached: !!exteriorModel,
    };

    // 兼容性测试
    const compatStart = performance.now();
    const legacyBinding = [0, 0, 0, 0, 0, 0];
    const isLegacy = PPTViewBindingUtils.isLegacyBinding(legacyBinding);
    report.tests.compatibility = {
      compatibilityCheckTime: Math.round(performance.now() - compatStart),
      legacyFormatSupported: isLegacy,
    };

    return JSON.stringify(report, null, 2);
  }
}

// 导出测试工具
export const pptEnhancedTester = new PPTEnhancedTester();

// 全局测试函数（可在控制台调用）
(window as any).testPPTEnhanced = {
  runFullTest: () => pptEnhancedTester.runFullTestSuite(),
  testPerformance: () => pptEnhancedTester.testSceneSwitchingPerformance(),
  testBinding: () => pptEnhancedTester.testViewBindingFeatures(),
  testCache: () => pptEnhancedTester.testCachingMechanism(),
  testCompatibility: () => pptEnhancedTester.testFormatCompatibility(),
  generateReport: () => pptEnhancedTester.generatePerformanceReport(),
};

console.log('🧪 PPT增强功能测试工具已加载');
console.log('💡 在控制台中使用 window.testPPTEnhanced 来运行测试');
console.log('📋 可用的测试方法:');
console.log('  - runFullTest(): 运行完整测试套件');
console.log('  - testPerformance(): 测试场景切换性能');
console.log('  - testBinding(): 测试视角绑定功能');
console.log('  - testCache(): 测试缓存机制');
console.log('  - testCompatibility(): 测试格式兼容性');
console.log('  - generateReport(): 生成性能报告');
