import * as THREE from 'three';
import { ModelLoaderManager } from '../load/ModelLoaderManager';
import { SceneManager } from '../SceneManager';
import { LightingManager } from '../LightingManager';
import { RenderingPipeline } from '../RenderingPipeline';
import { buildingData } from '@/data/buildingData';

/**
 * PPT演示模式专用模型加载器 2.0
 * 
 * 核心特性：
 * - 智能模型复用：直接使用已缓存的3D模型，无需重复加载
 * - 极速场景切换：支持外景和内景（1F、2F、3F、4F、5F、BF）的瞬间切换
 * - 零重复资源：完全避免DRACO解码器和GLTFLoader的重复创建
 * - 内存优化：不存储重复的模型数据
 * - 网络友好：完全避免重复下载
 */
export class PPTModelLoader {
  private static instance: PPTModelLoader | null = null;
  private modelLoaderManager: ModelLoaderManager | null = null;
  private scene: THREE.Scene | null = null;
  private currentSceneType: 'exterior' | 'interior' | null = null;
  private currentFloorId: string | null = null;

  private constructor() {
    this.modelLoaderManager = ModelLoaderManager.getInstance();
    this.scene = SceneManager.getInstance().scene;
  }

  static getInstance(): PPTModelLoader {
    if (!PPTModelLoader.instance) {
      PPTModelLoader.instance = new PPTModelLoader();
    }
    return PPTModelLoader.instance;
  }

  /**
   * 智能获取模型：缓存 → 当前场景 → 请求加载
   * @param modelPath 模型路径
   * @returns 模型对象或null
   */
  private getModelFromCache(modelPath: string): THREE.Object3D | null {
    if (!this.modelLoaderManager) return null;

    // 第一优先级：从ModelLoaderManager的loadedModels缓存获取
    const cachedModel = this.modelLoaderManager.loadedModels.get(modelPath);
    if (cachedModel) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PPT模型获取] 从ModelLoaderManager获取已缓存模型:', modelPath);
      }
      return cachedModel;
    }

    // 第二优先级：从当前场景中的models获取
    if (this.scene) {
      const sceneModel = this.scene.children.find(child => 
        child.userData.modelPath === modelPath
      );
      if (sceneModel) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PPT模型获取] 从当前场景获取模型:', modelPath);
        }
        return sceneModel;
      }
    }

    return null;
  }

  /**
   * 轻量级场景切换到外景
   * 复用已加载的模型，无需等待
   */
  async switchToExterior(): Promise<void> {
    try {
      const startTime = performance.now();
      
      if (process.env.NODE_ENV === 'development') {
        console.log('[PPT模型切换] 开始切换到外景模式');
      }

      // 获取外景模型路径
      const exteriorModelPath = buildingData.modelPath;
      if (!exteriorModelPath) {
        throw new Error('缺少外景模型路径');
      }

      // 智能获取外景模型
      let exteriorModel = this.getModelFromCache(exteriorModelPath);
      
      if (!exteriorModel) {
        // 兜底策略：请求ModelLoaderManager加载（而非自己加载）
        if (process.env.NODE_ENV === 'development') {
          console.log('[PPT模型获取] 缓存未命中，请求ModelLoaderManager加载外景模型');
        }
        await this.modelLoaderManager?.loadExteriorModelOnly();
        exteriorModel = this.getModelFromCache(exteriorModelPath);
      }

      if (!exteriorModel) {
        throw new Error('无法获取外景模型');
      }

      // 确保模型在场景中可见
      if (!this.scene?.children.includes(exteriorModel)) {
        // 隐藏当前楼层模型
        await this.hideCurrentFloorModels();
        
        // 显示外景模型
        this.scene?.add(exteriorModel);
        exteriorModel.visible = true;
      }

      // 应用外景场景设置（不切换场景类型，避免组件关闭）
      await this.applyExteriorSettings();

      this.currentSceneType = 'exterior';
      this.currentFloorId = null;

      const endTime = performance.now();
      if (process.env.NODE_ENV === 'development') {
        console.log(`[PPT模型切换] 外景模式切换完成，耗时: ${Math.round(endTime - startTime)}ms`);
      }

    } catch (error) {
      console.error('[PPT模型切换] 切换到外景失败:', error);
      throw error;
    }
  }

  /**
   * 轻量级场景切换到指定楼层
   * @param floorId 楼层ID（1F、2F、3F、4F、5F、BF）
   */
  async switchToFloor(floorId: string): Promise<void> {
    try {
      const startTime = performance.now();
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[PPT模型切换] 开始切换到楼层: ${floorId}`);
      }

      // 查找楼层数据
      const floorData = buildingData.floors.find(floor => floor.id === floorId);
      if (!floorData) {
        throw new Error(`找不到楼层数据: ${floorId}`);
      }

      // 智能获取楼层模型
      let floorModel = this.getModelFromCache(floorData.modelPath);
      
      if (!floorModel) {
        // 兜底策略：请求ModelLoaderManager加载
        if (process.env.NODE_ENV === 'development') {
          console.log(`[PPT模型获取] 缓存未命中，请求ModelLoaderManager加载楼层模型: ${floorId}`);
        }
        await this.modelLoaderManager?.showFloor(floorId);
        floorModel = this.getModelFromCache(floorData.modelPath);
      }

      if (!floorModel) {
        throw new Error(`无法获取楼层模型: ${floorId}`);
      }

      // 隐藏外景模型和其他楼层模型
      await this.hideExteriorModel();
      await this.hideOtherFloorModels(floorId);

      // 确保楼层模型在场景中可见
      if (!this.scene?.children.includes(floorModel)) {
        this.scene?.add(floorModel);
      }
      floorModel.visible = true;

      // 应用内景场景设置
      await this.applyInteriorSettings();

      this.currentSceneType = 'interior';
      this.currentFloorId = floorId;

      const endTime = performance.now();
      if (process.env.NODE_ENV === 'development') {
        console.log(`[PPT模型切换] 楼层${floorId}切换完成，耗时: ${Math.round(endTime - startTime)}ms`);
      }

    } catch (error) {
      console.error(`[PPT模型切换] 切换到楼层${floorId}失败:`, error);
      throw error;
    }
  }

  /**
   * 隐藏当前楼层模型
   */
  private async hideCurrentFloorModels(): Promise<void> {
    if (!this.scene) return;

    buildingData.floors.forEach(floor => {
      const floorModel = this.getModelFromCache(floor.modelPath);
      if (floorModel && this.scene?.children.includes(floorModel)) {
        floorModel.visible = false;
      }
    });
  }

  /**
   * 隐藏外景模型
   */
  private async hideExteriorModel(): Promise<void> {
    if (!this.scene) return;

    const exteriorModel = this.getModelFromCache(buildingData.modelPath);
    if (exteriorModel && this.scene.children.includes(exteriorModel)) {
      exteriorModel.visible = false;
    }
  }

  /**
   * 隐藏其他楼层模型
   */
  private async hideOtherFloorModels(currentFloorId: string): Promise<void> {
    if (!this.scene) return;

    buildingData.floors
      .filter(floor => floor.id !== currentFloorId)
      .forEach(floor => {
        const floorModel = this.getModelFromCache(floor.modelPath);
        if (floorModel && this.scene?.children.includes(floorModel)) {
          floorModel.visible = false;
        }
      });
  }

  /**
   * 应用外景场景设置
   */
  private async applyExteriorSettings(): Promise<void> {
    const sceneManager = SceneManager.getInstance();
    
    // 设置天空和光照
    if (sceneManager.renderer) {
      sceneManager.renderer.setClearColor(0x87ceeb, 1);
    }
    if (sceneManager.skyManager) {
      sceneManager.skyManager.show();
    }
    sceneManager.setDayLighting();

    // 应用外部场景光照设置
    const lightingManager = LightingManager.getInstance();
    if (lightingManager) {
      lightingManager.adjustLightingForWeather('clear');
    }

    // 应用平衡的色调映射
    const renderingPipeline = RenderingPipeline.getInstance();
    if (renderingPipeline) {
      renderingPipeline.setToneMapping(THREE.ACESFilmicToneMapping, 0.9, true);
    }
  }

  /**
   * 应用内景场景设置
   */
  private async applyInteriorSettings(): Promise<void> {
    const sceneManager = SceneManager.getInstance();
    
    // 设置内景光照
    sceneManager.setNightLighting();
    
    // 隐藏天空
    if (sceneManager.skyManager) {
      sceneManager.skyManager.hide();
    }

    // 设置内景背景色
    if (sceneManager.renderer) {
      sceneManager.renderer.setClearColor(0x1a1a1a, 1);
    }
  }

  /**
   * 获取当前场景类型
   */
  getCurrentSceneType(): 'exterior' | 'interior' | null {
    return this.currentSceneType;
  }

  /**
   * 获取当前楼层ID
   */
  getCurrentFloorId(): string | null {
    return this.currentFloorId;
  }

  /**
   * 获取场景信息描述
   */
  getSceneDescription(): string {
    if (this.currentSceneType === 'exterior') {
      return '外景';
    } else if (this.currentSceneType === 'interior' && this.currentFloorId) {
      const floorData = buildingData.floors.find(floor => floor.id === this.currentFloorId);
      return `内景-${floorData?.name || this.currentFloorId}`;
    }
    return '未知场景';
  }
}
