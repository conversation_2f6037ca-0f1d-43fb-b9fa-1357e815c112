<template>
  <div class="ppt-binding-indicator">
    <!-- 绑定状态指示器 -->
    <div class="binding-status">
      <div class="status-item">
        <div class="status-icon" :class="{ active: hasBinding }">
          <div class="status-dot" :class="{ bound: hasBinding }"></div>
        </div>
        <span class="status-text">{{ hasBinding ? '已绑定' : '未绑定' }}</span>
      </div>
    </div>

    <!-- 场景信息显示 -->
    <div v-if="hasBinding && binding" class="scene-info">
      <div class="scene-type">
        <span class="scene-label">场景:</span>
        <span class="scene-value">{{ sceneDescription }}</span>
      </div>

      <div v-if="binding.createTime" class="binding-time">
        <span class="time-label">绑定时间:</span>
        <span class="time-value">{{ formatTime(binding.createTime) }}</span>
      </div>

      <div v-if="binding.description" class="binding-desc">
        <span class="desc-label">描述:</span>
        <span class="desc-value">{{ binding.description }}</span>
      </div>
    </div>

    <!-- 进度条绑定点 -->
    <div class="progress-binding-points" v-if="totalSlides > 0">
      <div class="progress-label">绑定进度:</div>
      <div class="progress-bar">
        <div
          v-for="slideIndex in totalSlides"
          :key="slideIndex"
          class="progress-point"
          :class="{
            active: slideIndex - 1 === currentSlide,
            bound: hasSlideBinding(slideIndex - 1),
          }"
          @click="$emit('slide-change', slideIndex - 1)"
        >
          <div class="point-indicator"></div>
          <div class="point-tooltip">
            第{{ slideIndex }}页
            <br />
            {{ hasSlideBinding(slideIndex - 1) ? '已绑定' : '未绑定' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 场景切换控制 -->
    <div class="scene-controls">
      <div class="scene-control-label">场景切换:</div>
      <div class="scene-buttons">
        <button class="scene-btn exterior-btn" @click="$emit('switch-to-exterior')" :class="{ active: currentSceneType === 'exterior' }">
          <span class="scene-icon">🌅</span>
          外景
        </button>

        <div class="floor-selector">
          <button class="scene-btn interior-btn" @click="showFloorMenu = !showFloorMenu" :class="{ active: currentSceneType === 'interior' }">
            <span class="scene-icon">🏢</span>
            内景
            <span class="dropdown-icon">{{ showFloorMenu ? '▲' : '▼' }}</span>
          </button>

          <div v-if="showFloorMenu" class="floor-menu">
            <button
              v-for="floor in floors"
              :key="floor.id"
              class="floor-btn"
              @click="selectFloor(floor.id)"
              :class="{ active: currentFloorId === floor.id }"
            >
              <span class="floor-icon">{{ getFloorIcon(floor.id) }}</span>
              {{ floor.name }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作按钮 -->
    <div class="quick-actions">
      <button class="action-btn bind-btn" @click="$emit('bind-current')" :disabled="!canBind">
        <span class="btn-icon">📍</span>
        绑定当前视角
      </button>

      <button v-if="hasBinding" class="action-btn preview-btn" @click="$emit('preview-binding')">
        <span class="btn-icon">👁️</span>
        预览绑定
      </button>

      <button v-if="hasBinding" class="action-btn clear-btn" @click="$emit('clear-binding')">
        <span class="btn-icon">🗑️</span>
        清除绑定
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { useGlobalThreeStore, type PPTViewBinding } from '@/views/scene/store/globalThreeStore';
  import { buildingData } from '@/data/buildingData';

  interface Props {
    currentSlide: number;
    totalSlides: number;
    canBind?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    canBind: true,
  });

  const emit = defineEmits<{
    'bind-current': [];
    'preview-binding': [];
    'clear-binding': [];
    'slide-change': [slideIndex: number];
    'switch-to-exterior': [];
    'switch-to-floor': [floorId: string];
  }>();

  const globalThreeStore = useGlobalThreeStore();

  // 响应式数据
  const showFloorMenu = ref(false);

  // 计算属性
  const binding = computed(() => {
    return globalThreeStore.getPPTViewBinding(props.currentSlide);
  });

  const floors = computed(() => {
    return buildingData.floors;
  });

  const currentSceneType = computed(() => {
    if (globalThreeStore.currentView === 1) {
      return 'exterior';
    } else if (globalThreeStore.currentView === 2) {
      return 'interior';
    }
    return null;
  });

  const currentFloorId = computed(() => {
    return globalThreeStore.currentFloorId;
  });

  const hasBinding = computed(() => {
    return binding.value !== undefined;
  });

  const sceneDescription = computed(() => {
    if (!binding.value) return '未知场景';

    if (binding.value.sceneType === 'exterior') {
      return '外景';
    } else if (binding.value.sceneType === 'interior' && binding.value.floorId) {
      const floorData = buildingData.floors.find((floor) => floor.id === binding.value!.floorId);
      return `内景-${floorData?.name || binding.value.floorId}`;
    }

    return '当前场景';
  });

  // 方法
  const hasSlideBinding = (slideIndex: number): boolean => {
    return globalThreeStore.getPPTViewBinding(slideIndex) !== undefined;
  };

  const formatTime = (timeString: string): string => {
    try {
      const date = new Date(timeString);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return '未知时间';
    }
  };

  const selectFloor = (floorId: string) => {
    showFloorMenu.value = false;
    emit('switch-to-floor', floorId);
  };

  const getFloorIcon = (floorId: string): string => {
    const iconMap: Record<string, string> = {
      '1F': '1️⃣',
      '2F': '2️⃣',
      '3F': '3️⃣',
      '4F': '4️⃣',
      '5F': '5️⃣',
      BF: '🅱️',
    };
    return iconMap[floorId] || '🏢';
  };
</script>

<style scoped>
  .ppt-binding-indicator {
    background: rgba(10, 15, 28, 0.95);
    border: 1px solid rgba(59, 142, 230, 0.3);
    border-radius: 0.5vw;
    padding: 1vw;
    margin: 0.5vw 0;
    backdrop-filter: blur(10px);
  }

  .binding-status {
    display: flex;
    align-items: center;
    margin-bottom: 0.8vw;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 0.5vw;
  }

  .status-icon {
    position: relative;
    width: 1.2vw;
    height: 1.2vw;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .status-icon.active {
    border-color: #3b8ee6;
    box-shadow: 0 0 0.5vw rgba(59, 142, 230, 0.5);
  }

  .status-dot {
    width: 0.6vw;
    height: 0.6vw;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
  }

  .status-dot.bound {
    background: #3b8ee6;
    box-shadow: 0 0 0.3vw rgba(59, 142, 230, 0.8);
  }

  .status-text {
    color: #ffffff;
    font-size: 0.8vw;
    font-weight: 500;
  }

  .scene-info {
    background: rgba(59, 142, 230, 0.1);
    border-radius: 0.3vw;
    padding: 0.8vw;
    margin-bottom: 0.8vw;
    border-left: 3px solid #3b8ee6;
  }

  .scene-type,
  .binding-time,
  .binding-desc {
    display: flex;
    align-items: center;
    margin-bottom: 0.3vw;
    font-size: 0.7vw;
  }

  .scene-label,
  .time-label,
  .desc-label {
    color: rgba(255, 255, 255, 0.7);
    margin-right: 0.5vw;
    min-width: 3vw;
  }

  .scene-value,
  .time-value,
  .desc-value {
    color: #ffffff;
    font-weight: 500;
  }

  .progress-binding-points {
    margin-bottom: 1vw;
  }

  .progress-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.7vw;
    margin-bottom: 0.5vw;
  }

  .progress-bar {
    display: flex;
    gap: 0.3vw;
    flex-wrap: wrap;
  }

  .progress-point {
    position: relative;
    width: 1vw;
    height: 1vw;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .point-indicator {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
  }

  .progress-point.bound .point-indicator {
    background: #3b8ee6;
    border-color: #3b8ee6;
    box-shadow: 0 0 0.3vw rgba(59, 142, 230, 0.6);
  }

  .progress-point.active .point-indicator {
    transform: scale(1.3);
    border-width: 2px;
  }

  .point-tooltip {
    position: absolute;
    bottom: 120%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.3vw 0.5vw;
    border-radius: 0.2vw;
    font-size: 0.6vw;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
  }

  .progress-point:hover .point-tooltip {
    opacity: 1;
  }

  .quick-actions {
    display: flex;
    gap: 0.5vw;
    flex-wrap: wrap;
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 0.3vw;
    padding: 0.4vw 0.8vw;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.3vw;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 0.7vw;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .action-btn:hover:not(:disabled) {
    background: rgba(59, 142, 230, 0.3);
    border-color: #3b8ee6;
    transform: translateY(-1px);
  }

  .action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .bind-btn:hover:not(:disabled) {
    background: rgba(34, 197, 94, 0.3);
    border-color: #22c55e;
  }

  .preview-btn:hover {
    background: rgba(59, 142, 230, 0.3);
    border-color: #3b8ee6;
  }

  .clear-btn:hover {
    background: rgba(239, 68, 68, 0.3);
    border-color: #ef4444;
  }

  .btn-icon {
    font-size: 0.8vw;
  }

  /* 场景控制样式 */
  .scene-controls {
    margin-bottom: 1vw;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.3vw;
    padding: 0.8vw;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .scene-control-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.7vw;
    margin-bottom: 0.5vw;
  }

  .scene-buttons {
    display: flex;
    gap: 0.5vw;
    align-items: center;
  }

  .scene-btn {
    display: flex;
    align-items: center;
    gap: 0.3vw;
    padding: 0.4vw 0.8vw;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.3vw;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 0.7vw;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .scene-btn:hover {
    background: rgba(59, 142, 230, 0.3);
    border-color: #3b8ee6;
    transform: translateY(-1px);
  }

  .scene-btn.active {
    background: rgba(59, 142, 230, 0.5);
    border-color: #3b8ee6;
    box-shadow: 0 0 0.5vw rgba(59, 142, 230, 0.3);
  }

  .scene-icon {
    font-size: 0.8vw;
  }

  .dropdown-icon {
    font-size: 0.6vw;
    margin-left: 0.2vw;
  }

  .floor-selector {
    position: relative;
  }

  .floor-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(10, 15, 28, 0.95);
    border: 1px solid rgba(59, 142, 230, 0.3);
    border-radius: 0.3vw;
    backdrop-filter: blur(10px);
    z-index: 1000;
    margin-top: 0.2vw;
    max-height: 15vw;
    overflow-y: auto;
  }

  .floor-btn {
    display: flex;
    align-items: center;
    gap: 0.5vw;
    width: 100%;
    padding: 0.5vw 0.8vw;
    border: none;
    background: transparent;
    color: #ffffff;
    font-size: 0.7vw;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .floor-btn:last-child {
    border-bottom: none;
  }

  .floor-btn:hover {
    background: rgba(59, 142, 230, 0.2);
  }

  .floor-btn.active {
    background: rgba(59, 142, 230, 0.4);
    color: #3b8ee6;
  }

  .floor-icon {
    font-size: 0.8vw;
  }
</style>
