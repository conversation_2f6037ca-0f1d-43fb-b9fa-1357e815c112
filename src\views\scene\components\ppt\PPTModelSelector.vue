<template>
  <div class="ppt-model-selector">
    <div class="selector-header">
      <span class="selector-icon">🏗️</span>
      <span class="selector-title">模型选择</span>
    </div>
    
    <div class="model-options">
      <div 
        v-for="model in availableModels"
        :key="model.id"
        class="model-option"
        :class="{ 
          active: selectedModelId === model.id,
          bound: hasModelBinding(model.id)
        }"
        @click="selectModel(model.id)"
      >
        <div class="model-icon">{{ model.icon }}</div>
        <div class="model-info">
          <div class="model-name">{{ model.name }}</div>
          <div class="model-desc">{{ model.description }}</div>
        </div>
        <div class="model-status">
          <div v-if="hasModelBinding(model.id)" class="status-indicator bound">
            <span class="status-dot"></span>
            已绑定
          </div>
          <div v-else class="status-indicator">
            <span class="status-dot"></span>
            未绑定
          </div>
        </div>
      </div>
    </div>

    <div class="selector-actions">
      <button 
        class="action-btn bind-model-btn"
        @click="bindCurrentModel"
        :disabled="!selectedModelId || !canBind"
      >
        <span class="btn-icon">🔗</span>
        绑定到当前页
      </button>
      
      <button 
        class="action-btn preview-model-btn"
        @click="previewModel"
        :disabled="!selectedModelId"
      >
        <span class="btn-icon">👁️</span>
        预览模型
      </button>
      
      <button 
        v-if="hasCurrentSlideModelBinding"
        class="action-btn clear-model-btn"
        @click="clearModelBinding"
      >
        <span class="btn-icon">🗑️</span>
        清除绑定
      </button>
    </div>

    <div v-if="currentSlideModelBinding" class="current-binding-info">
      <div class="binding-info-header">
        <span class="info-icon">📋</span>
        当前页绑定信息
      </div>
      <div class="binding-details">
        <div class="binding-item">
          <span class="item-label">模型:</span>
          <span class="item-value">{{ getCurrentModelName() }}</span>
        </div>
        <div class="binding-item">
          <span class="item-label">绑定时间:</span>
          <span class="item-value">{{ formatTime(currentSlideModelBinding.createTime) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { useGlobalThreeStore } from '@/views/scene/store/globalThreeStore';
  import { ModelPaths } from '@/api/scene';

  interface Props {
    currentSlide: number;
    canBind?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    canBind: true,
  });

  const emit = defineEmits<{
    'model-selected': [modelId: string];
    'model-bound': [slideIndex: number, modelId: string];
    'model-preview': [modelId: string];
    'model-binding-cleared': [slideIndex: number];
  }>();

  const globalThreeStore = useGlobalThreeStore();
  const selectedModelId = ref<string>('');

  // 可用模型列表
  const availableModels = computed(() => [
    {
      id: 'exterior',
      name: '外景模型',
      description: '建筑外观全景',
      icon: '🌅',
      path: ModelPaths.ExteriorModel,
      type: 'exterior' as const,
    },
    {
      id: '1f',
      name: '1楼模型',
      description: '一楼内部结构',
      icon: '1️⃣',
      path: ModelPaths.Floor1,
      type: 'floor' as const,
      floorId: '1F',
    },
    {
      id: '2f',
      name: '2楼模型',
      description: '二楼内部结构',
      icon: '2️⃣',
      path: ModelPaths.Floor2,
      type: 'floor' as const,
      floorId: '2F',
    },
    {
      id: '3f',
      name: '3楼模型',
      description: '三楼内部结构',
      icon: '3️⃣',
      path: ModelPaths.Floor3,
      type: 'floor' as const,
      floorId: '3F',
    },
    {
      id: '4f',
      name: '4楼模型',
      description: '四楼内部结构',
      icon: '4️⃣',
      path: ModelPaths.Floor4,
      type: 'floor' as const,
      floorId: '4F',
    },
    {
      id: '5f',
      name: '5楼模型',
      description: '五楼内部结构',
      icon: '5️⃣',
      path: ModelPaths.Floor5,
      type: 'floor' as const,
      floorId: '5F',
    },
    {
      id: 'bf',
      name: '水泵房模型',
      description: '地下水泵房',
      icon: '🅱️',
      path: ModelPaths.FloorB1,
      type: 'floor' as const,
      floorId: 'BF',
    },
    {
      id: 'drone',
      name: '无人机模型',
      description: '动态无人机',
      icon: '🚁',
      path: ModelPaths.DroneDynamic,
      type: 'special' as const,
    },
  ]);

  // 计算属性
  const currentSlideModelBinding = computed(() => {
    const binding = globalThreeStore.getPPTViewBinding(props.currentSlide);
    return binding?.modelId ? binding : null;
  });

  const hasCurrentSlideModelBinding = computed(() => {
    return currentSlideModelBinding.value !== null;
  });

  // 方法
  const hasModelBinding = (modelId: string): boolean => {
    return globalThreeStore.pptDemonstration.viewBindings.some(
      binding => binding.modelId === modelId
    );
  };

  const selectModel = (modelId: string) => {
    selectedModelId.value = modelId;
    emit('model-selected', modelId);
  };

  const bindCurrentModel = async () => {
    if (!selectedModelId.value || !props.canBind) return;

    try {
      console.log(`[PPTModelSelector] 绑定模型 ${selectedModelId.value} 到幻灯片 ${props.currentSlide + 1}`);
      
      // 获取选中的模型信息
      const selectedModel = availableModels.value.find(m => m.id === selectedModelId.value);
      if (!selectedModel) {
        throw new Error('未找到选中的模型');
      }

      // 创建增强的视角绑定，包含模型信息
      const binding = await globalThreeStore.createEnhancedPPTViewBinding(props.currentSlide, {
        name: `第${props.currentSlide + 1}页 - ${selectedModel.name}`,
        description: `绑定模型: ${selectedModel.name} (${selectedModel.description})`,
        includeSceneInfo: true,
        modelId: selectedModelId.value,
        modelPath: selectedModel.path,
        modelType: selectedModel.type,
        floorId: selectedModel.floorId,
      });

      if (binding) {
        console.log(`[PPTModelSelector] 模型绑定成功:`, binding);
        emit('model-bound', props.currentSlide, selectedModelId.value);
        
        if (window.$message) {
          window.$message.success(`第${props.currentSlide + 1}页已绑定${selectedModel.name}`);
        }
      } else {
        throw new Error('绑定创建失败');
      }
    } catch (error) {
      console.error('[PPTModelSelector] 模型绑定失败:', error);
      if (window.$message) {
        window.$message.error('模型绑定失败，请重试');
      }
    }
  };

  const previewModel = async () => {
    if (!selectedModelId.value) return;

    try {
      console.log(`[PPTModelSelector] 预览模型: ${selectedModelId.value}`);
      
      const selectedModel = availableModels.value.find(m => m.id === selectedModelId.value);
      if (!selectedModel) {
        throw new Error('未找到选中的模型');
      }

      // 根据模型类型切换场景
      if (selectedModel.type === 'exterior') {
        // 切换到外景
        const { PPTModelLoader } = await import('../../lib/ppt/PPTModelLoader');
        const pptModelLoader = PPTModelLoader.getInstance();
        await pptModelLoader.switchToExterior();
      } else if (selectedModel.type === 'floor' && selectedModel.floorId) {
        // 切换到楼层
        const { PPTModelLoader } = await import('../../lib/ppt/PPTModelLoader');
        const pptModelLoader = PPTModelLoader.getInstance();
        await pptModelLoader.switchToFloor(selectedModel.floorId);
      }

      emit('model-preview', selectedModelId.value);
      
      if (window.$message) {
        window.$message.info(`正在预览${selectedModel.name}`);
      }
    } catch (error) {
      console.error('[PPTModelSelector] 模型预览失败:', error);
      if (window.$message) {
        window.$message.error('模型预览失败');
      }
    }
  };

  const clearModelBinding = () => {
    if (!hasCurrentSlideModelBinding.value) return;

    globalThreeStore.removePPTViewBinding(props.currentSlide);
    emit('model-binding-cleared', props.currentSlide);
    
    if (window.$message) {
      window.$message.success(`已清除第${props.currentSlide + 1}页的模型绑定`);
    }
  };

  const getCurrentModelName = (): string => {
    if (!currentSlideModelBinding.value?.modelId) return '未知模型';
    
    const model = availableModels.value.find(m => m.id === currentSlideModelBinding.value!.modelId);
    return model?.name || '未知模型';
  };

  const formatTime = (timeString?: string): string => {
    if (!timeString) return '未知时间';
    
    try {
      const date = new Date(timeString);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return '未知时间';
    }
  };

  // 初始化选中当前页绑定的模型
  if (currentSlideModelBinding.value?.modelId) {
    selectedModelId.value = currentSlideModelBinding.value.modelId;
  }
</script>

<style scoped>
  .ppt-model-selector {
    background: rgba(10, 15, 28, 0.95);
    border: 1px solid rgba(59, 142, 230, 0.3);
    border-radius: 0.5vw;
    padding: 1vw;
    backdrop-filter: blur(10px);
  }

  .selector-header {
    display: flex;
    align-items: center;
    gap: 0.5vw;
    margin-bottom: 1vw;
    padding-bottom: 0.5vw;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  .selector-icon {
    font-size: 1.2vw;
  }

  .selector-title {
    color: #ffffff;
    font-size: 0.9vw;
    font-weight: bold;
  }

  .model-options {
    display: flex;
    flex-direction: column;
    gap: 0.5vw;
    margin-bottom: 1vw;
    max-height: 20vw;
    overflow-y: auto;
  }

  .model-option {
    display: flex;
    align-items: center;
    gap: 0.8vw;
    padding: 0.8vw;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.3vw;
    background: rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .model-option:hover {
    background: rgba(59, 142, 230, 0.2);
    border-color: rgba(59, 142, 230, 0.5);
    transform: translateY(-1px);
  }

  .model-option.active {
    background: rgba(59, 142, 230, 0.3);
    border-color: #3b8ee6;
    box-shadow: 0 0 0.5vw rgba(59, 142, 230, 0.3);
  }

  .model-option.bound {
    border-left: 3px solid #22c55e;
  }

  .model-icon {
    font-size: 1.5vw;
    flex-shrink: 0;
  }

  .model-info {
    flex: 1;
  }

  .model-name {
    color: #ffffff;
    font-size: 0.8vw;
    font-weight: 500;
    margin-bottom: 0.2vw;
  }

  .model-desc {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.6vw;
  }

  .model-status {
    flex-shrink: 0;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 0.3vw;
    font-size: 0.6vw;
    color: rgba(255, 255, 255, 0.6);
  }

  .status-indicator.bound {
    color: #22c55e;
  }

  .status-dot {
    width: 0.4vw;
    height: 0.4vw;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
  }

  .status-indicator.bound .status-dot {
    background: #22c55e;
    box-shadow: 0 0 0.2vw rgba(34, 197, 94, 0.6);
  }

  .selector-actions {
    display: flex;
    gap: 0.5vw;
    flex-wrap: wrap;
    margin-bottom: 1vw;
  }

  .action-btn {
    display: flex;
    align-items: center;
    gap: 0.3vw;
    padding: 0.4vw 0.8vw;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.3vw;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 0.7vw;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .action-btn:hover:not(:disabled) {
    background: rgba(59, 142, 230, 0.3);
    border-color: #3b8ee6;
    transform: translateY(-1px);
  }

  .action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .bind-model-btn:hover:not(:disabled) {
    background: rgba(34, 197, 94, 0.3);
    border-color: #22c55e;
  }

  .preview-model-btn:hover:not(:disabled) {
    background: rgba(59, 142, 230, 0.3);
    border-color: #3b8ee6;
  }

  .clear-model-btn:hover {
    background: rgba(239, 68, 68, 0.3);
    border-color: #ef4444;
  }

  .btn-icon {
    font-size: 0.8vw;
  }

  .current-binding-info {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 0.3vw;
    padding: 0.8vw;
  }

  .binding-info-header {
    display: flex;
    align-items: center;
    gap: 0.5vw;
    margin-bottom: 0.5vw;
    color: #22c55e;
    font-size: 0.8vw;
    font-weight: 500;
  }

  .info-icon {
    font-size: 1vw;
  }

  .binding-details {
    display: flex;
    flex-direction: column;
    gap: 0.3vw;
  }

  .binding-item {
    display: flex;
    align-items: center;
    font-size: 0.7vw;
  }

  .item-label {
    color: rgba(255, 255, 255, 0.7);
    margin-right: 0.5vw;
    min-width: 3vw;
  }

  .item-value {
    color: #ffffff;
    font-weight: 500;
  }

  /* 滚动条样式 */
  .model-options::-webkit-scrollbar {
    width: 0.3vw;
  }

  .model-options::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.15vw;
  }

  .model-options::-webkit-scrollbar-thumb {
    background: rgba(59, 142, 230, 0.6);
    border-radius: 0.15vw;
  }

  .model-options::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 142, 230, 0.8);
  }
</style>
