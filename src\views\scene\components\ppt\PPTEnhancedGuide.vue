<template>
  <div class="ppt-enhanced-guide">
    <div class="guide-header">
      <h2 class="guide-title">
        <span class="title-icon">🚀</span>
        PPT演示模式增强功能使用说明
      </h2>
      <div class="version-badge">v2.0</div>
    </div>

    <div class="guide-content">
      <!-- 概述 -->
      <section class="guide-section">
        <h3 class="section-title">
          <span class="section-icon">📋</span>
          概述
        </h3>
        <div class="section-content">
          <p>全新设计的PPT演示模式，实现了<strong>真正的轻量化</strong>：</p>
          <ul class="feature-list">
            <li><span class="feature-icon">⚡</span><strong>智能模型复用</strong>：直接使用已缓存的3D模型，无需重复加载</li>
            <li><span class="feature-icon">🚀</span><strong>极速场景切换</strong>：支持外景和内景（1F、2F、3F、4F、5F、BF）的瞬间切换</li>
            <li><span class="feature-icon">💾</span><strong>零重复资源</strong>：完全避免DRACO解码器和GLTFLoader的重复创建</li>
          </ul>
        </div>
      </section>

      <!-- 性能优势 -->
      <section class="guide-section">
        <h3 class="section-title">
          <span class="section-icon">🏆</span>
          性能优势
        </h3>
        <div class="section-content">
          <div class="comparison-grid">
            <div class="comparison-item old">
              <h4>传统方式（已废弃）</h4>
              <ul>
                <li>❌ 重复创建GLTFLoader和DRACOLoader</li>
                <li>❌ 重新设置DRACO解码器路径</li>
                <li>❌ 重复加载已存在的模型文件</li>
                <li>❌ 浪费内存和网络资源</li>
              </ul>
            </div>
            <div class="comparison-item new">
              <h4>新设计（当前）</h4>
              <ul>
                <li>✅ <strong>智能复用</strong>：直接使用ModelLoaderManager的已缓存模型</li>
                <li>✅ <strong>零加载时间</strong>：模型切换几乎瞬间完成</li>
                <li>✅ <strong>内存优化</strong>：不存储重复的模型数据</li>
                <li>✅ <strong>网络友好</strong>：完全避免重复下载</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <!-- 主要功能 -->
      <section class="guide-section">
        <h3 class="section-title">
          <span class="section-icon">🎯</span>
          主要功能
        </h3>
        <div class="section-content">
          <div class="feature-grid">
            <div class="feature-card">
              <h4>增强的视角绑定</h4>
              <p>现在可以为每张PPT图片绑定：</p>
              <ul>
                <li>相机位置和目标点</li>
                <li>场景类型（外景/内景）</li>
                <li>楼层信息（如果是内景）</li>
                <li>绑定时间和描述</li>
              </ul>
            </div>
            <div class="feature-card">
              <h4>智能场景切换</h4>
              <p>在切换PPT图片时，系统会：</p>
              <ul>
                <li>智能模型获取：优先从缓存获取</li>
                <li>瞬间场景切换：复用已加载的模型</li>
                <li>精确相机定位：移动到绑定的视角位置</li>
                <li>友好用户反馈：显示切换完成的提示信息</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <!-- 使用步骤 -->
      <section class="guide-section">
        <h3 class="section-title">
          <span class="section-icon">📝</span>
          使用步骤
        </h3>
        <div class="section-content">
          <div class="steps-container">
            <div class="step-item">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>选择场景</h4>
                <p>在3D场景中导航到想要绑定的位置，可以切换到外景（总览模式）或内景（选择具体楼层）</p>
              </div>
            </div>
            <div class="step-item">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>调整视角</h4>
                <p>使用鼠标操作调整到最佳观察角度：拖拽旋转视角、滚轮缩放、右键拖拽平移</p>
              </div>
            </div>
            <div class="step-item">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>绑定视角</h4>
                <p>在PPT演示模式下，切换到要绑定的图片，点击"绑定当前视角"按钮</p>
              </div>
            </div>
            <div class="step-item">
              <div class="step-number">4</div>
              <div class="step-content">
                <h4>享受极速切换</h4>
                <p>切换到其他图片，然后回到绑定的图片，观察场景<strong>瞬间</strong>切换到绑定的场景和视角 ⚡</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 数据格式 -->
      <section class="guide-section">
        <h3 class="section-title">
          <span class="section-icon">💾</span>
          数据格式
        </h3>
        <div class="section-content">
          <div class="format-grid">
            <div class="format-item">
              <h4>新格式（增强绑定）</h4>
              <pre class="code-block">{
  "position": [x, y, z],
  "target": [x, y, z],
  "sceneType": "interior|exterior",
  "floorId": "1F|2F|3F|4F|5F|BF",
  "createTime": "ISO时间戳",
  "name": "绑定名称"
}</pre>
            </div>
            <div class="format-item">
              <h4>旧格式（兼容支持）</h4>
              <pre class="code-block">[px, py, pz, tx, ty, tz]</pre>
            </div>
          </div>
        </div>
      </section>

      <!-- 界面提示 -->
      <section class="guide-section">
        <h3 class="section-title">
          <span class="section-icon">🎨</span>
          界面提示
        </h3>
        <div class="section-content">
          <ul class="ui-features">
            <li><span class="ui-icon">🟢</span><strong>绑定状态指示器</strong>：绿色点表示已绑定，灰色点表示未绑定</li>
            <li><span class="ui-icon">🏢</span><strong>场景信息显示</strong>：显示当前场景类型（外景/内景-楼层）</li>
            <li><span class="ui-icon">📊</span><strong>进度条绑定点</strong>：每张图片在进度条上都有绑定状态指示</li>
            <li><span class="ui-icon">✅</span><strong>成功提示</strong>：绑定和切换时显示详细的场景信息</li>
          </ul>
        </div>
      </section>

      <!-- 技术优势 -->
      <section class="guide-section">
        <h3 class="section-title">
          <span class="section-icon">⚙️</span>
          技术优势
        </h3>
        <div class="section-content">
          <div class="tech-grid">
            <div class="tech-item">
              <span class="tech-icon">⚡</span>
              <h4>极速切换</h4>
              <p>复用已缓存模型，切换时间 &lt; 100ms</p>
            </div>
            <div class="tech-item">
              <span class="tech-icon">💾</span>
              <h4>内存高效</h4>
              <p>零模型重复，节省大量内存</p>
            </div>
            <div class="tech-item">
              <span class="tech-icon">🌐</span>
              <h4>网络友好</h4>
              <p>完全避免重复下载，节省带宽</p>
            </div>
            <div class="tech-item">
              <span class="tech-icon">🔄</span>
              <h4>向下兼容</h4>
              <p>支持旧版本的简单视角绑定</p>
            </div>
            <div class="tech-item">
              <span class="tech-icon">🎯</span>
              <h4>状态保持</h4>
              <p>PPT演示过程中保持演示模式状态</p>
            </div>
            <div class="tech-item">
              <span class="tech-icon">🛡️</span>
              <h4>智能回退</h4>
              <p>缓存未命中时自动请求加载</p>
            </div>
          </div>
        </div>
      </section>
    </div>

    <div class="guide-footer">
      <div class="footer-note">
        <span class="note-icon">💡</span>
        <strong>注意事项：</strong>首次加载项目时需要等待模型缓存完成，绑定数据保存在服务器端，支持多用户共享
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  // 这个组件是纯展示组件，不需要额外的逻辑
</script>

<style scoped>
  .ppt-enhanced-guide {
    background: linear-gradient(135deg, #0a0f1c 0%, #1a2332 100%);
    color: #ffffff;
    padding: 2vw;
    border-radius: 0.8vw;
    max-height: 80vh;
    overflow-y: auto;
    font-family: 'Microsoft YaHei', sans-serif;
  }

  .guide-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2vw;
    border-bottom: 2px solid rgba(59, 142, 230, 0.3);
    padding-bottom: 1vw;
  }

  .guide-title {
    display: flex;
    align-items: center;
    gap: 0.5vw;
    font-size: 1.5vw;
    font-weight: bold;
    margin: 0;
  }

  .title-icon {
    font-size: 1.8vw;
  }

  .version-badge {
    background: linear-gradient(45deg, #3b8ee6, #22c55e);
    color: white;
    padding: 0.3vw 0.8vw;
    border-radius: 1vw;
    font-size: 0.7vw;
    font-weight: bold;
  }

  .guide-section {
    margin-bottom: 2vw;
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 0.5vw;
    font-size: 1.1vw;
    font-weight: bold;
    margin-bottom: 1vw;
    color: #3b8ee6;
  }

  .section-icon {
    font-size: 1.2vw;
  }

  .section-content {
    padding-left: 1.5vw;
  }

  .feature-list {
    list-style: none;
    padding: 0;
  }

  .feature-list li {
    display: flex;
    align-items: center;
    gap: 0.5vw;
    margin-bottom: 0.5vw;
    font-size: 0.8vw;
  }

  .feature-icon {
    font-size: 1vw;
  }

  .comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1vw;
    margin-top: 1vw;
  }

  .comparison-item {
    padding: 1vw;
    border-radius: 0.5vw;
    border: 1px solid;
  }

  .comparison-item.old {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
  }

  .comparison-item.new {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
  }

  .comparison-item h4 {
    margin: 0 0 0.5vw 0;
    font-size: 0.9vw;
  }

  .comparison-item ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .comparison-item li {
    margin-bottom: 0.3vw;
    font-size: 0.7vw;
  }

  .feature-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1vw;
  }

  .feature-card {
    background: rgba(59, 142, 230, 0.1);
    border: 1px solid rgba(59, 142, 230, 0.3);
    border-radius: 0.5vw;
    padding: 1vw;
  }

  .feature-card h4 {
    margin: 0 0 0.5vw 0;
    color: #3b8ee6;
    font-size: 0.9vw;
  }

  .feature-card p {
    margin: 0 0 0.5vw 0;
    font-size: 0.7vw;
  }

  .feature-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .feature-card li {
    margin-bottom: 0.2vw;
    font-size: 0.6vw;
    padding-left: 1vw;
    position: relative;
  }

  .feature-card li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #3b8ee6;
  }

  .steps-container {
    display: flex;
    flex-direction: column;
    gap: 1vw;
  }

  .step-item {
    display: flex;
    align-items: flex-start;
    gap: 1vw;
  }

  .step-number {
    width: 2vw;
    height: 2vw;
    border-radius: 50%;
    background: linear-gradient(45deg, #3b8ee6, #22c55e);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9vw;
    flex-shrink: 0;
  }

  .step-content h4 {
    margin: 0 0 0.3vw 0;
    color: #3b8ee6;
    font-size: 0.9vw;
  }

  .step-content p {
    margin: 0;
    font-size: 0.7vw;
    line-height: 1.4;
  }

  .format-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1vw;
  }

  .format-item h4 {
    margin: 0 0 0.5vw 0;
    color: #3b8ee6;
    font-size: 0.8vw;
  }

  .code-block {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.3vw;
    padding: 0.8vw;
    font-family: 'Courier New', monospace;
    font-size: 0.6vw;
    line-height: 1.4;
    overflow-x: auto;
  }

  .ui-features {
    list-style: none;
    padding: 0;
  }

  .ui-features li {
    display: flex;
    align-items: center;
    gap: 0.5vw;
    margin-bottom: 0.5vw;
    font-size: 0.8vw;
  }

  .ui-icon {
    font-size: 1vw;
  }

  .tech-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1vw;
  }

  .tech-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5vw;
    padding: 1vw;
    text-align: center;
  }

  .tech-icon {
    font-size: 1.5vw;
    display: block;
    margin-bottom: 0.5vw;
  }

  .tech-item h4 {
    margin: 0 0 0.3vw 0;
    color: #3b8ee6;
    font-size: 0.8vw;
  }

  .tech-item p {
    margin: 0;
    font-size: 0.6vw;
    color: rgba(255, 255, 255, 0.8);
  }

  .guide-footer {
    margin-top: 2vw;
    padding-top: 1vw;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .footer-note {
    display: flex;
    align-items: center;
    gap: 0.5vw;
    font-size: 0.7vw;
    color: rgba(255, 255, 255, 0.8);
  }

  .note-icon {
    font-size: 1vw;
  }

  /* 滚动条样式 */
  .ppt-enhanced-guide::-webkit-scrollbar {
    width: 0.3vw;
  }

  .ppt-enhanced-guide::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.15vw;
  }

  .ppt-enhanced-guide::-webkit-scrollbar-thumb {
    background: rgba(59, 142, 230, 0.6);
    border-radius: 0.15vw;
  }

  .ppt-enhanced-guide::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 142, 230, 0.8);
  }
</style>
