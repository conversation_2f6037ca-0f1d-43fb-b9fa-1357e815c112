<template>
  <div class="simple-ppt-player h-full flex flex-col bg-[#0a0f1c]">
    <!-- 标题栏 -->
    <div class="flex items-center justify-between p-4 bg-[#1a2332] border-b border-[#2a3441]">
      <div class="flex items-center">
        <FileTextOutlined class="text-[#3B8EE6] text-lg mr-2" />
        <span class="text-white text-sm font-medium">图片演示</span>
      </div>
      <div class="flex items-center space-x-2">
        <span class="text-gray-400 text-xs">{{ currentSlide + 1 }} / {{ totalSlides }}</span>
        <!-- 视角绑定状态指示 -->
        <div v-if="totalSlides > 0" class="flex items-center space-x-1">
          <div
            class="w-2 h-2 rounded-full transition-colors"
            :class="hasCurrentViewBinding ? 'bg-green-500' : 'bg-gray-500'"
            :title="hasCurrentViewBinding ? '当前图片已绑定视角' : '当前图片未绑定视角'"
          ></div>
          <span class="text-xs" :class="hasCurrentViewBinding ? 'text-green-400' : 'text-gray-500'">
            {{ hasCurrentViewBinding ? '已绑定' : '未绑定' }}
          </span>
        </div>
        <CustomButton icon="upload" @click="showPPTManager = true" size="small" type="primary"> PPT管理 </CustomButton>
        <a-button size="small" type="text" @click="$emit('close')" class="text-gray-400 hover:text-white">
          <CloseOutlined />
        </a-button>
      </div>
    </div>

    <!-- 图片内容区域 -->
    <div class="flex-1 relative overflow-hidden">
      <!-- 图片显示区域 -->
      <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-900 to-gray-800 relative">
        <!-- 有图片时显示图片 -->
        <div v-if="images.length > 0 && currentImage" class="w-full h-full flex items-center justify-center relative">
          <img :src="currentImage.url" :alt="currentImage.name" class="max-w-full max-h-full object-contain" @error="handleImageError" />

          <!-- 视角绑定状态指示器 -->
          <div class="absolute top-4 left-4 flex items-center space-x-2 bg-black/60 px-3 py-2 rounded-lg backdrop-blur-sm">
            <div
              class="w-3 h-3 rounded-full flex items-center justify-center transition-all duration-300"
              :class="hasCurrentViewBinding ? 'bg-green-500 shadow-lg shadow-green-500/50' : 'bg-gray-500'"
            >
              <div v-if="hasCurrentViewBinding" class="w-1.5 h-1.5 bg-white rounded-full"></div>
            </div>
            <span class="text-white text-sm font-medium"> 视角{{ hasCurrentViewBinding ? '已绑定' : '未绑定' }} </span>
            <div v-if="hasCurrentViewBinding" class="text-green-400 text-xs"> ✓ </div>
          </div>
        </div>

        <!-- 无图片时显示提示 -->
        <div v-else class="text-center text-gray-400">
          <div class="text-6xl mb-4">📷</div>
          <div class="text-xl mb-2">暂无图片</div>
          <div class="text-sm">点击"PPT管理"按钮管理PPT列表</div>
        </div>

        <!-- 导航覆盖层 -->
        <div class="absolute inset-0 pointer-events-none">
          <!-- 左侧导航区域 -->
          <div
            class="absolute left-0 top-0 w-16 h-full pointer-events-auto cursor-pointer hover:bg-black/10 transition-colors flex items-center justify-center"
            @click="previousSlide"
            v-if="currentSlide > 0"
          >
            <LeftOutlined class="text-gray-600 text-2xl opacity-70 hover:opacity-100" />
          </div>

          <!-- 右侧导航区域 -->
          <div
            class="absolute right-0 top-0 w-16 h-full pointer-events-auto cursor-pointer hover:bg-black/10 transition-colors flex items-center justify-center"
            @click="nextSlide"
            v-if="currentSlide < totalSlides - 1"
          >
            <RightOutlined class="text-gray-600 text-2xl opacity-70 hover:opacity-100" />
          </div>
        </div>
      </div>
    </div>

    <!-- 控制栏 - 调整底部间距避免被遮挡 -->
    <div class="p-4 pb-16 bg-[#1a2332] border-t border-[#2a3441]">
      <div class="flex items-center justify-between">
        <!-- 播放控制 -->
        <div class="flex items-center space-x-2">
          <CustomButton :icon="isAutoPlaying ? 'pause' : 'play'" @click="toggleAutoPlay" type="primary">
            {{ isAutoPlaying ? '暂停' : '自动播放' }}
          </CustomButton>
          <CustomSelect v-model:value="autoPlayInterval" :options="intervalOptions" :disabled="!isAutoPlaying" style="width: 100px" />
        </div>

        <!-- 页面导航 -->
        <div class="flex items-center space-x-2">
          <CustomButton icon="left" @click="previousSlide" :disabled="currentSlide <= 0"> 上一页 </CustomButton>
          <CustomInputNumber v-model:value="jumpToSlide" :min="1" :max="totalSlides" style="width: 80px" @pressEnter="goToSlide" />
          <CustomButton icon="right" @click="nextSlide" :disabled="currentSlide >= totalSlides - 1"> 下一页 </CustomButton>
        </div>

        <!-- 视角绑定 -->
        <div class="flex items-center space-x-2">
          <CustomButton
            :icon="hasCurrentViewBinding ? 'check' : 'eye'"
            @click="bindCurrentSlideView"
            :type="hasCurrentViewBinding ? 'default' : 'primary'"
            :class="hasCurrentViewBinding ? 'border-green-500 text-green-500' : ''"
          >
            {{ hasCurrentViewBinding ? '更新视角' : '绑定当前视角' }}
          </CustomButton>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="mt-3">
        <!-- 自定义进度条显示绑定状态 -->
        <div class="relative">
          <!-- 进度条背景 -->
          <div class="h-2 bg-gray-700 rounded-full relative overflow-hidden">
            <!-- 当前进度 -->
            <div
              class="h-full bg-blue-500 rounded-full transition-all duration-300"
              :style="{ width: totalSlides > 1 ? `${(currentSlide / (totalSlides - 1)) * 100}%` : '0%' }"
            ></div>

            <!-- 绑定状态指示点 -->
            <div
              v-for="index in totalSlides"
              :key="index"
              class="absolute top-1/2 -translate-y-1/2 w-3 h-3 rounded-full border-2 border-gray-800 cursor-pointer transition-all duration-200 hover:scale-125"
              :class="{
                'bg-green-500 border-green-400 shadow-lg shadow-green-500/50': slideBindingStatus[index - 1],
                'bg-gray-500 border-gray-400': !slideBindingStatus[index - 1],
                'ring-2 ring-blue-400 ring-opacity-75': currentSlide === index - 1,
              }"
              :style="{
                left: totalSlides > 1 ? `calc(${((index - 1) / (totalSlides - 1)) * 100}% - 6px)` : 'calc(50% - 6px)',
              }"
              :title="`第${index}张图片${slideBindingStatus[index - 1] ? ' - 已绑定视角' : ' - 未绑定视角'}`"
              @click="goToSlide(index - 1)"
            >
              <!-- 绑定状态内部指示 -->
              <div v-if="slideBindingStatus[index - 1]" class="absolute inset-0.5 bg-white rounded-full flex items-center justify-center">
                <div class="w-1 h-1 bg-green-500 rounded-full"></div>
              </div>
            </div>
          </div>

          <!-- 进度条下方的标签 -->
          <div class="flex justify-between items-center mt-2 text-xs text-gray-400">
            <span>第 1 张</span>
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-1">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>已绑定视角</span>
              </div>
              <div class="flex items-center space-x-1">
                <div class="w-2 h-2 bg-gray-500 rounded-full"></div>
                <span>未绑定视角</span>
              </div>
            </div>
            <span>第 {{ totalSlides }} 张</span>
          </div>
        </div>
      </div>
    </div>

    <!-- PPT管理模态框 - 使用ModalDialog组件 -->
    <ModalDialog
      v-model:visible="showPPTManager"
      title="PPT管理"
      width="70vw"
      height="75vh"
      showFooter
      cancelText="关闭"
      confirmText="确认"
      @cancel="showPPTManager = false"
      @confirm="closePPTManager"
    >
      <div class="ppt-manager p-4 h-full overflow-y-auto">
        <!-- PPT列表 -->
        <div class="mb-6">
          <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-medium text-white">PPT列表</h4>
            <CustomButton icon="plus" @click="showCreatePPT = true" type="primary" size="small"> 新增PPT </CustomButton>
          </div>

          <!-- PPT列表网格 -->
          <div v-if="pptList.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[50vh] overflow-y-auto custom-scrollbar">
            <div
              v-for="(ppt, index) in pptList"
              :key="`${ppt.id}_${index}`"
              class="relative group border-2 rounded-lg overflow-hidden cursor-pointer transition-all"
              :class="{
                'border-blue-500 bg-blue-500/10':
                  currentPPT && currentPPT.id === ppt.id && currentPPT.name === ppt.name && currentPPT.orderNum === ppt.orderNum,
                'border-gray-600 hover:border-gray-400': !(
                  currentPPT &&
                  currentPPT.id === ppt.id &&
                  currentPPT.name === ppt.name &&
                  currentPPT.orderNum === ppt.orderNum
                ),
              }"
              @click="selectPPT(ppt)"
            >
              <!-- PPT封面 -->
              <div class="aspect-video bg-gray-800 flex items-center justify-center">
                <img v-if="ppt.coverImage" :src="ppt.coverImage" :alt="ppt.name" class="max-w-full max-h-full object-cover" />
                <div v-else class="text-gray-400">
                  <FileTextOutlined class="text-4xl mb-2" />
                  <div class="text-sm">暂无封面</div>
                </div>
              </div>

              <!-- PPT信息 -->
              <div class="p-3 bg-gray-800 text-white">
                <div class="font-medium truncate mb-1" :title="ppt.name">
                  {{ ppt.name || '未命名PPT' }}
                </div>
                <div class="text-xs text-gray-400">
                  {{ ppt.imageCount || 0 }} 张图片
                  <span v-if="getPPTViewBindingCount(ppt)" class="text-blue-400 ml-1"> ({{ getPPTViewBindingCount(ppt) }}个视角) </span>
                </div>
                <div class="text-xs text-gray-400"> 创建时间: {{ formatDate(ppt.createTime) }} </div>
              </div>

              <!-- 操作按钮 -->
              <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                <a-button size="small" type="primary" @click.stop="editPPT(ppt)" class="!p-1">
                  <EditOutlined />
                </a-button>
                <a-button size="small" danger type="primary" @click.stop="deletePPT(ppt.id)" class="!p-1">
                  <DeleteOutlined />
                </a-button>
              </div>

              <!-- 当前选中标识 -->
              <div
                v-if="currentPPT && currentPPT.id === ppt.id && currentPPT.name === ppt.name && currentPPT.orderNum === ppt.orderNum"
                class="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded"
              >
                当前
              </div>
            </div>
          </div>

          <!-- 无PPT提示 -->
          <div v-else class="text-center text-gray-400 py-8">
            <div class="text-4xl mb-2">📁</div>
            <div>暂无PPT，请创建新的PPT</div>
          </div>
        </div>

        <!-- 操作说明 -->
        <div class="text-sm text-gray-300 bg-gray-800/50 p-3 rounded">
          <div class="font-medium mb-1">操作说明：</div>
          <ul class="space-y-1">
            <li>• 点击PPT卡片可以选择并加载该PPT</li>
            <li>• 点击编辑按钮可以修改PPT内容</li>
            <li>• 点击删除按钮可以删除PPT</li>
            <li>• PPT数据保存在服务器端</li>
            <li>• 蓝色数字显示已绑定的视角数量</li>
            <li>• 在编辑PPT时可以为每张图片绑定3D场景视角</li>
          </ul>
        </div>
      </div>
    </ModalDialog>

    <!-- 新增/编辑PPT模态框 -->
    <ModalDialog
      v-model:visible="showCreatePPT"
      :title="editingPPT ? '编辑PPT' : '新增PPT'"
      width="80vw"
      height="80vh"
      showFooter
      cancelText="取消"
      confirmText="保存"
      @cancel="cancelCreatePPT"
      @confirm="savePPT"
    >
      <div class="create-ppt p-4 h-full overflow-y-auto">
        <!-- PPT基本信息 -->
        <div class="mb-6">
          <div class="mb-4">
            <label class="block text-white text-sm font-medium mb-2">PPT名称</label>
            <a-input v-model:value="pptForm.name" placeholder="请输入PPT名称" class="bg-gray-800 border-gray-600 text-white" />
          </div>
        </div>

        <!-- 图片上传区域 -->
        <div class="mb-6">
          <div class="mb-4">
            <label class="block text-white text-sm font-medium mb-2">PPT图片</label>
            <a-upload-dragger
              v-model:fileList="fileList"
              name="file"
              multiple
              accept="image/*"
              :before-upload="beforeUpload"
              :show-upload-list="false"
              @change="handleUploadChange"
            >
              <p class="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p class="ant-upload-text">点击或拖拽图片到此区域上传</p>
              <p class="ant-upload-hint">
                支持单个或批量上传，支持 JPG、PNG、GIF 格式<br />
                <span class="text-green-600">✨ 自动智能压缩，单张图片可达20MB</span>
              </p>
            </a-upload-dragger>
          </div>

          <!-- 图片列表 -->
          <div v-if="pptForm.images.length > 0" class="mb-4">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-white">图片列表 ({{ pptForm.images.length }})</h4>
              <a-button @click="clearCurrentPPTImages" danger size="small">清空图片</a-button>
            </div>

            <!-- 图片网格 -->
            <div class="grid grid-cols-4 gap-4 max-h-[40vh] overflow-y-auto custom-scrollbar">
              <div
                v-for="(image, index) in pptForm.images"
                :key="image.id"
                class="relative group border-2 border-gray-600 rounded-lg overflow-hidden cursor-move"
                draggable="true"
                @dragstart="handleDragStart(index)"
                @dragover.prevent
                @drop="handleDrop(index)"
              >
                <!-- 图片预览 -->
                <div class="aspect-video bg-gray-800 flex items-center justify-center">
                  <img :src="image.url" :alt="image.name" class="max-w-full max-h-full object-cover" @error="handleImageError" />
                </div>

                <!-- 图片信息 -->
                <div class="p-2 bg-gray-800 text-white">
                  <div class="text-xs truncate" :title="image.name">
                    {{ image.name }}
                  </div>
                  <div class="text-xs text-gray-400">
                    {{ formatFileSize(image.size) }}
                    <span v-if="image.compressionRatio" class="text-green-600 ml-1"> (压缩{{ image.compressionRatio }}%) </span>
                    <br />
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <a-button size="small" danger type="primary" @click="removeImageFromPPT(index)" class="!p-1">
                    <DeleteOutlined />
                  </a-button>
                </div>

                <!-- 序号 -->
                <div class="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                  {{ index + 1 }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ModalDialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
  import {
    CloseOutlined,
    LeftOutlined,
    RightOutlined,
    InboxOutlined,
    DeleteOutlined,
    EditOutlined,
    PlusOutlined,
    FileTextOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { defHttp } from '/@/utils/http/axios';
  import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
  import { useGlobalThreeStore } from '../../store/globalThreeStore';
  import type { PPTViewBinding } from '../../store/globalThreeStore';
  import ViewBindingModal from './ViewBindingModal.vue';
  import ModalDialog from '../../components/ModalDialog.vue';
  import { CameraController } from '../../lib/CameraController';
  import CustomButton from './CustomButton.vue';
  import CustomSelect from './CustomSelect.vue';
  import CustomInputNumber from './CustomInputNumber.vue';
  import CustomSlider from './CustomSlider.vue';

  // 图片接口定义
  interface ImageItem {
    id: string;
    name: string;
    url: string;
    size: number;
    type: string;
    originalSize?: number;
    compressionRatio?: string;
  }

  // PPT接口定义
  interface PPTItem {
    id: number;
    photoUrl?: string;
    dataJson?: string;
    orderNum?: number;
    name?: string;
    coverImage?: string;
    imageCount?: number;
    createTime?: string;
    images?: ImageItem[];
  }

  // PPT表单接口定义
  interface PPTForm {
    id?: number;
    name: string;
    images: ImageItem[];
  }

  // Props
  interface Props {
    pptPath: string;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    close: [];
    slideChange: [slideIndex: number];
  }>();

  // Store
  const globalThreeStore = useGlobalThreeStore();

  // 响应式数据
  const currentSlide = ref(0);
  const totalSlides = ref(0);
  const jumpToSlide = ref(1);
  const isAutoPlaying = ref(false);
  const autoPlayInterval = ref(5000);

  const showPPTManager = ref(false);
  const showCreatePPT = ref(false);
  const editingPPT = ref(false);

  // 图片相关数据（当前显示的PPT图片）
  const images = ref<ImageItem[]>([]);
  const fileList = ref([]);
  const draggedIndex = ref<number | null>(null);
  const processingFiles = ref<Set<string>>(new Set()); // 正在处理的文件集合

  // PPT管理相关数据
  const pptList = ref<PPTItem[]>([]);
  const currentPPT = ref<PPTItem | null>(null);

  const pptForm = ref<PPTForm>({
    name: '',
    images: [],
  });
  const loading = ref(false);

  // 自动播放定时器
  let autoPlayTimer: number | null = null;

  // 选项数据
  const intervalOptions = ref([
    { value: 3000, label: '3秒' },
    { value: 5000, label: '5秒' },
    { value: 8000, label: '8秒' },
    { value: 10000, label: '10秒' },
  ]);

  // 计算属性
  const currentImage = computed(() => {
    return images.value[currentSlide.value] || null;
  });

  // 当前PPT的视角绑定数据缓存
  const currentPPTViewBindings = ref<Record<number, boolean>>({});

  // 检查当前幻灯片是否有视角绑定
  const hasCurrentViewBinding = computed(() => {
    return currentPPTViewBindings.value[currentSlide.value] || false;
  });

  // 获取所有幻灯片的视角绑定状态
  const slideBindingStatus = computed(() => {
    return currentPPTViewBindings.value;
  });

  // 更新当前PPT的视角绑定状态
  const updatePPTViewBindings = async () => {
    if (!currentPPT.value) {
      currentPPTViewBindings.value = {};
      return;
    }

    try {
      const response = await apiCall(
        {
          url: `/pptView/${currentPPT.value.id}`,
          method: 'GET',
        },
        {
          silent: true,
          errorMessage: '',
          showLoading: false,
        }
      );

      if (response) {
        const dataJson = response.dataJson ? JSON.parse(response.dataJson) : {};
        const bindings: Record<number, boolean> = {};

        if (dataJson.views) {
          Object.keys(dataJson.views).forEach((key) => {
            bindings[parseInt(key)] = true;
          });
        }

        currentPPTViewBindings.value = bindings;
        console.log('[视角绑定] 更新绑定状态:', bindings);
      }
    } catch (error) {
      console.warn('更新视角绑定状态失败:', error);
      currentPPTViewBindings.value = {};
    }
  };

  // 错误去重机制
  const errorMessageCache = ref<Set<string>>(new Set());
  const ERROR_CACHE_DURATION = 3000; // 3秒内相同错误不重复显示

  // 错误去重工具函数
  const showUniqueError = (errorMsg: string, duration = ERROR_CACHE_DURATION) => {
    if (errorMessageCache.value.has(errorMsg)) {
      return; // 如果错误已存在，不重复显示
    }

    errorMessageCache.value.add(errorMsg);
    message.error(errorMsg);

    // 设置定时器清除缓存
    setTimeout(() => {
      errorMessageCache.value.delete(errorMsg);
    }, duration);
  };

  // API调用包装器，支持静默模式
  const apiCall = async <T = any,>(
    apiConfig: any,
    options: {
      silent?: boolean;
      errorMessage?: string;
      showLoading?: boolean;
    } = {}
  ): Promise<T | null> => {
    const { silent = false, errorMessage, showLoading = false } = options;

    let loadingHide: (() => void) | null = null;

    try {
      if (showLoading) {
        loadingHide = message.loading('处理中...', 0);
      }

      // 为API调用添加静默模式配置
      const response = await defHttp.request<T>({
        ...apiConfig,
        // 静默模式下不显示全局错误提示
        ...(silent && {
          requestOptions: {
            ...apiConfig.requestOptions,
            errorMessageMode: 'none',
          },
        }),
      });

      return response;
    } catch (error: any) {
      if (!silent && errorMessage) {
        // 使用去重机制显示错误
        showUniqueError(errorMessage);
      }
      console.error('API调用失败:', error);
      return null;
    } finally {
      if (loadingHide) {
        loadingHide();
      }
    }
  };

  // 方法
  const initializePPT = async () => {
    try {
      await loadPPTList();
      loadViewBindingsFromStorage();
      console.log('PPT演示初始化成功');
    } catch (error) {
      console.error('PPT演示初始化失败:', error);
      showUniqueError('PPT演示初始化失败');
    }
  };

  // 从localStorage加载视角绑定数据
  const loadViewBindingsFromStorage = () => {
    globalThreeStore.loadPPTViewBindingsFromStorage();
  };

  // PPT管理相关方法
  // 加载PPT列表
  const loadPPTList = async () => {
    try {
      loading.value = true;

      const response = await apiCall(
        {
          url: '/pptView',
          method: 'GET',
          params: {
            pageNo: 1,
            pageSize: 100,
          },
        },
        {
          silent: true, // 静默模式，不显示全局错误
          errorMessage: '加载PPT列表失败',
          showLoading: false, // 使用自己的loading状态
        }
      );

      if (!response) {
        pptList.value = [];
        return;
      }

      // 解析JSON的辅助函数
      const safeJsonParse = (jsonStr: string, defaultValue: any = null) => {
        try {
          return jsonStr ? JSON.parse(jsonStr) : defaultValue;
        } catch (error) {
          console.warn('JSON解析失败:', jsonStr, error);
          return defaultValue;
        }
      };

      const processData = (data: any[]) => {
        return data.map((item: any) => {
          const dataJson = safeJsonParse(item.dataJson, {});
          const photoUrls = safeJsonParse(item.photoUrl, []);

          return {
            id: item.id,
            name: dataJson.name || `PPT ${item.id}`,
            coverImage: Array.isArray(photoUrls) && photoUrls.length > 0 ? photoUrls[0] : null,
            imageCount: Array.isArray(photoUrls) ? photoUrls.length : 0,
            createTime: item.createTime || new Date().toISOString(),
            photoUrl: item.photoUrl,
            dataJson: item.dataJson,
            orderNum: item.orderNum,
          };
        });
      };

      if (response.records) {
        pptList.value = processData(response.records);
      } else if (Array.isArray(response)) {
        pptList.value = processData(response);
      } else {
        pptList.value = [];
      }
    } catch (error) {
      console.error('加载PPT列表失败:', error);
      pptList.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 选择PPT
  const selectPPT = async (ppt: PPTItem) => {
    try {
      currentPPT.value = ppt;

      // 从服务器加载PPT详情
      const response = await apiCall(
        {
          url: `/pptView/${ppt.id}`,
          method: 'GET',
        },
        {
          silent: true,
          errorMessage: '加载PPT失败',
          showLoading: true,
        }
      );

      if (response) {
        // 解析图片数据
        const safeJsonParse = (jsonStr: string, defaultValue: any = null) => {
          try {
            return jsonStr ? JSON.parse(jsonStr) : defaultValue;
          } catch (error) {
            console.warn('JSON解析失败:', jsonStr, error);
            return defaultValue;
          }
        };

        console.log('PPT原始数据:', response);

        const imageUrls = safeJsonParse(response.photoUrl, []);
        const pptData = safeJsonParse(response.dataJson, {});

        console.log('解析后的图片URLs:', imageUrls);
        console.log('解析后的PPT数据:', pptData);

        if (!Array.isArray(imageUrls) || imageUrls.length === 0) {
          console.warn('PPT没有有效的图片数据:', response.photoUrl);
          message.warning(`PPT "${ppt.name}" 没有有效的图片数据`);
          images.value = [];
        } else {
          images.value = imageUrls.map((url: string, index: number) => ({
            id: `${ppt.id}_${index}`,
            name: `图片${index + 1}`,
            url: getFileAccessHttpUrl(url),
            size: 0,
            type: 'image/jpeg',
          }));
        }

        updateTotalSlides();
        currentSlide.value = 0;
        jumpToSlide.value = 1;

        // 更新视角绑定状态
        await updatePPTViewBindings();

        if (images.value.length > 0) {
          message.success(`已加载PPT: ${ppt.name}，共 ${images.value.length} 张图片`);
        } else {
          message.warning(`PPT: ${ppt.name} 加载成功，但没有图片内容`);
        }
      }
    } catch (error) {
      console.error('加载PPT失败:', error);
    }
  };

  // 关闭PPT管理
  const closePPTManager = () => {
    showPPTManager.value = false;
  };

  // 编辑PPT
  const editPPT = async (ppt: PPTItem) => {
    editingPPT.value = true;

    try {
      // 加载PPT详情
      const response = await apiCall(
        {
          url: `/pptView/${ppt.id}`,
          method: 'GET',
        },
        {
          silent: true,
          errorMessage: '加载PPT详情失败',
          showLoading: true,
        }
      );

      if (response) {
        const safeJsonParse = (jsonStr: string, defaultValue: any = null) => {
          try {
            return jsonStr ? JSON.parse(jsonStr) : defaultValue;
          } catch (error) {
            console.warn('JSON解析失败:', jsonStr, error);
            return defaultValue;
          }
        };

        const imageUrls = safeJsonParse(response.photoUrl, []);
        const pptData = safeJsonParse(response.dataJson, {});

        pptForm.value = {
          id: ppt.id,
          name: pptData.name || ppt.name || '',
          images: Array.isArray(imageUrls)
            ? imageUrls.map((url: string, index: number) => ({
                id: `${ppt.id}_${index}`,
                name: `图片${index + 1}`,
                url: url,
                size: 0,
                type: 'image/jpeg',
              }))
            : [],
        };

        showCreatePPT.value = true;
      }
    } catch (error) {
      console.error('加载PPT详情失败:', error);
    }
  };

  // 删除PPT
  const deletePPT = async (id: number) => {
    try {
      const response = await apiCall(
        {
          url: '/pptView',
          method: 'DELETE',
          params: {
            idList: id,
          },
        },
        {
          silent: true,
          errorMessage: '删除PPT失败',
          showLoading: true,
        }
      );

      if (response !== null) {
        message.success('PPT删除成功');
        await loadPPTList();

        // 如果删除的是当前PPT，清空显示
        if (currentPPT.value?.id === id) {
          currentPPT.value = null;
          images.value = [];
          updateTotalSlides();
        }
      }
    } catch (error) {
      console.error('删除PPT失败:', error);
    }
  };

  // 保存PPT
  const savePPT = async () => {
    if (!pptForm.value.name.trim()) {
      showUniqueError('请输入PPT名称');
      return;
    }

    if (pptForm.value.images.length === 0) {
      showUniqueError('请上传至少一张图片');
      return;
    }

    try {
      loading.value = true;

      // 准备数据
      const photoUrls = pptForm.value.images.map((img: { url: any }) => img.url);

      // 暂时不保存视角绑定数据
      const viewBindings = {};

      const dataJson = {
        name: pptForm.value.name,
        imageCount: pptForm.value.images.length,
        createTime: new Date().toISOString(),
        viewBindings: viewBindings, // 保存视角绑定数据
      };

      const requestData = {
        name: pptForm.value.name, // 直接添加name字段到接口参数
        photoUrl: JSON.stringify(photoUrls),
        dataJson: JSON.stringify(dataJson),
        orderNum: editingPPT.value && pptForm.value.id ? pptForm.value.id : pptList.value.length + 1,
      };

      let response = null;
      if (editingPPT.value && pptForm.value.id) {
        // 编辑模式
        response = await apiCall(
          {
            url: '/pptView',
            method: 'PUT',
            data: {
              id: pptForm.value.id,
              ...requestData,
            },
          },
          {
            silent: true,
            errorMessage: 'PPT更新失败',
            showLoading: false,
          }
        );

        if (response !== null) {
          message.success('PPT更新成功');
        }
      } else {
        // 新增模式
        response = await apiCall(
          {
            url: '/pptView',
            method: 'POST',
            data: requestData,
          },
          {
            silent: true,
            errorMessage: 'PPT创建失败',
            showLoading: false,
          }
        );

        if (response !== null) {
          message.success('PPT创建成功');
        }
      }

      if (response !== null) {
        // 重新加载列表
        await loadPPTList();
        // 关闭弹窗
        cancelCreatePPT();
      }
    } catch (error) {
      console.error('保存PPT失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 取消创建PPT
  const cancelCreatePPT = () => {
    showCreatePPT.value = false;
    editingPPT.value = false;
    pptForm.value = {
      name: '',
      images: [],
    };
    fileList.value = [];

    // 如果正在编辑，需要重新加载当前PPT的视角绑定数据
    if (currentPPT.value && editingPPT.value) {
      selectPPT(currentPPT.value);
    }
  };

  // 从PPT中移除图片
  const removeImageFromPPT = (index: number) => {
    const image = pptForm.value.images[index];
    pptForm.value.images.splice(index, 1);
    message.success(`图片 ${image.name} 已删除`);
  };

  // 清空当前PPT的图片
  const clearCurrentPPTImages = () => {
    pptForm.value.images = [];
    fileList.value = [];
    message.success('已清空所有图片');
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    if (!dateString) return '未知';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // 获取PPT的视角绑定数量
  const getPPTViewBindingCount = (ppt: PPTItem) => {
    try {
      const pptData = ppt.dataJson ? JSON.parse(ppt.dataJson) : {};
      if (pptData.viewBindings) {
        return Object.keys(pptData.viewBindings).length;
      }
      return 0;
    } catch (error) {
      return 0;
    }
  };

  // 更新总页数
  const updateTotalSlides = () => {
    totalSlides.value = images.value.length;
    globalThreeStore.setPPTTotalSlides(totalSlides.value);

    // 如果当前页超出范围，重置到第一页
    if (currentSlide.value >= totalSlides.value && totalSlides.value > 0) {
      currentSlide.value = 0;
      jumpToSlide.value = 1;
    }
  };

  const previousSlide = () => {
    if (currentSlide.value > 0) {
      goToSlide(currentSlide.value - 1);
    }
  };

  const nextSlide = () => {
    if (currentSlide.value < totalSlides.value - 1) {
      goToSlide(currentSlide.value + 1);
    }
  };

  const goToSlide = (slideIndex?: number) => {
    const targetSlide = slideIndex !== undefined ? slideIndex : jumpToSlide.value - 1;
    if (targetSlide >= 0 && targetSlide < totalSlides.value) {
      currentSlide.value = targetSlide;
      jumpToSlide.value = targetSlide + 1;

      // 更新store状态
      globalThreeStore.setPPTCurrentSlide(targetSlide);

      // 触发视角切换
      triggerViewChange(targetSlide);

      // 触发事件
      emit('slideChange', targetSlide);
    }
  };

  // 图片上传相关方法
  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      showUniqueError('只能上传图片文件!');
      return false;
    }

    // 放宽文件大小限制，因为我们会自动压缩
    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
      showUniqueError('图片大小不能超过 20MB!');
      return false;
    }

    return false; // 阻止自动上传，手动处理
  };

  const handleUploadChange = (info: any) => {
    const { fileList } = info;

    // 生成文件唯一标识符
    const getFileKey = (file: any) => `${file.name}_${file.size}_${file.lastModified || file.lastModifiedDate?.getTime() || ''}`;

    // 处理新上传的文件，使用更严格的重复检查
    const newFiles = fileList.filter((file: any) => {
      if (!file.originFileObj || file.status === 'done') {
        return false;
      }

      const fileKey = getFileKey(file);

      // 检查是否正在处理
      if (processingFiles.value.has(fileKey)) {
        console.log(`文件 ${file.name} 正在处理中，跳过`);
        return false;
      }

      // 检查目标图片数组中是否已存在
      const targetImages = showCreatePPT.value ? pptForm.value.images : images.value;
      const exists = targetImages.some((img: ImageItem) => img.name === file.name && (img.originalSize === file.size || img.size === file.size));

      if (exists) {
        console.log(`文件 ${file.name} 已存在，跳过`);
        return false;
      }

      return true;
    });

    if (newFiles.length === 0) {
      console.log('没有新文件需要处理');
      return;
    }

    console.log(`开始处理 ${newFiles.length} 个新文件`);

    let processedCount = 0;
    const totalFiles = newFiles.length;
    const addedImages: ImageItem[] = []; // 记录本次添加的图片

    newFiles.forEach((file: any) => {
      const fileKey = getFileKey(file);

      // 标记为正在处理
      processingFiles.value.add(fileKey);

      // 显示上传进度
      const hideLoading = message.loading(`正在上传图片 ${file.name}...`, 0);

      // 使用JeecgBoot的上传接口
      uploadImage(file.originFileObj)
        .then((imageUrl) => {
          hideLoading();

          const imageItem: ImageItem = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            name: file.name,
            url: imageUrl,
            size: file.size,
            type: file.type,
            originalSize: file.size,
          };

          // 再次检查是否已存在（防止并发添加）
          const targetImages = showCreatePPT.value ? pptForm.value.images : images.value;
          const exists = targetImages.some((img: ImageItem) => img.name === file.name && (img.originalSize === file.size || img.size === file.size));

          if (!exists) {
            // 如果在创建PPT模式，添加到pptForm.images
            if (showCreatePPT.value) {
              pptForm.value.images.push(imageItem);
            } else {
              images.value.push(imageItem);
            }
            addedImages.push(imageItem);
            console.log(`图片 ${file.name} 上传成功: ${imageUrl}`);
          } else {
            console.log(`图片 ${file.name} 已存在，跳过添加`);
          }

          processedCount++;
          processingFiles.value.delete(fileKey);

          // 所有文件处理完成后统一更新
          if (processedCount === totalFiles) {
            if (addedImages.length > 0) {
              if (!showCreatePPT.value) {
                // 如果不在创建PPT模式，更新总页数
                updateTotalSlides();
              }

              // 清空fileList，避免重复处理
              fileList.value = [];

              if (addedImages.length === 1) {
                message.success(`图片 ${addedImages[0].name} 上传成功`);
              } else {
                message.success(`成功上传 ${addedImages.length} 张图片`);
              }
            } else {
              fileList.value = [];
              message.info('所有文件都已存在，未添加新图片');
            }
          }
        })
        .catch((error) => {
          hideLoading();
          console.error(`图片 ${file.name} 上传失败:`, error);
          showUniqueError(`图片 ${file.name} 上传失败`);
          processedCount++;
          processingFiles.value.delete(fileKey);

          if (processedCount === totalFiles) {
            fileList.value = [];
          }
        });
    });
  };

  // 上传图片到服务器
  const uploadImage = async (file: File): Promise<string> => {
    try {
      const response = await defHttp.uploadFile(
        {
          url: '/sys/common/upload',
        },
        {
          file: file,
          name: 'file',
        },
        {
          isReturnResponse: true,
        }
      );

      if (response && response.success && response.message) {
        // JeecgBoot返回的是相对路径，使用getFileAccessHttpUrl转换为完整URL
        return getFileAccessHttpUrl(response.message);
      } else {
        throw new Error('上传失败');
      }
    } catch (error) {
      console.error('上传图片失败:', error);
      throw error;
    }
  };

  // 拖拽排序相关方法
  const handleDragStart = (index: number) => {
    draggedIndex.value = index;
  };

  const handleDrop = (targetIndex: number) => {
    if (draggedIndex.value !== null && draggedIndex.value !== targetIndex) {
      // 在PPT编辑模式下处理拖拽
      if (showCreatePPT.value) {
        const draggedItem = pptForm.value.images[draggedIndex.value];
        pptForm.value.images.splice(draggedIndex.value, 1);
        pptForm.value.images.splice(targetIndex, 0, draggedItem);
      } else {
        const draggedItem = images.value[draggedIndex.value];
        images.value.splice(draggedIndex.value, 1);
        images.value.splice(targetIndex, 0, draggedItem);

        // 更新当前页面索引
        if (currentSlide.value === draggedIndex.value) {
          currentSlide.value = targetIndex;
          jumpToSlide.value = targetIndex + 1;
        } else if (currentSlide.value === targetIndex) {
          currentSlide.value = draggedIndex.value;
          jumpToSlide.value = draggedIndex.value + 1;
        }
      }

      message.success('图片顺序已调整');
    }
    draggedIndex.value = null;
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 处理图片加载错误
  const handleImageError = () => {
    showUniqueError('图片加载失败');
  };

  // 图片压缩函数
  const compressImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // 计算压缩后的尺寸
        const maxWidth = 1920;
        const maxHeight = 1080;
        let { width, height } = img;

        // 按比例缩放
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width = Math.round(width * ratio);
          height = Math.round(height * ratio);
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制图片
        ctx?.drawImage(img, 0, 0, width, height);

        // 尝试不同的压缩质量，直到文件大小合适
        const tryCompress = (quality: number): string => {
          const dataUrl = canvas.toDataURL('image/jpeg', quality);
          const size = Math.round((dataUrl.length * 3) / 4); // base64大小估算

          // 如果大小合适或质量已经很低，返回结果
          if (size <= 200 * 1024 || quality <= 0.3) {
            return dataUrl;
          }

          // 否则降低质量继续压缩
          return tryCompress(quality - 0.1);
        };

        try {
          const result = tryCompress(0.8); // 从80%质量开始
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('图片加载失败'));
      };

      // 读取文件
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target?.result as string;
      };
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      reader.readAsDataURL(file);
    });
  };

  const toggleAutoPlay = () => {
    isAutoPlaying.value = !isAutoPlaying.value;
    globalThreeStore.setPPTPlaying(isAutoPlaying.value);

    if (isAutoPlaying.value) {
      startAutoPlay();
    } else {
      stopAutoPlay();
    }
  };

  const startAutoPlay = () => {
    stopAutoPlay(); // 清除现有定时器
    autoPlayTimer = window.setInterval(() => {
      if (currentSlide.value < totalSlides.value - 1) {
        nextSlide();
      } else {
        // 到达最后一页，停止自动播放
        toggleAutoPlay();
      }
    }, autoPlayInterval.value);
  };

  const stopAutoPlay = () => {
    if (autoPlayTimer) {
      clearInterval(autoPlayTimer);
      autoPlayTimer = null;
    }
  };

  const triggerViewChange = async (slideIndex: number) => {
    console.log(`[视角切换] 切换到幻灯片 ${slideIndex + 1}`);

    if (!currentPPT.value) {
      console.log('[视角切换] 没有选中的PPT');
      return;
    }

    try {
      // 获取当前PPT数据
      const response = await apiCall(
        {
          url: `/pptView/${currentPPT.value.id}`,
          method: 'GET',
        },
        {
          silent: true,
          errorMessage: '',
          showLoading: false,
        }
      );

      if (!response) {
        console.log('[视角切换] 获取PPT数据失败');
        return;
      }

      // 解析视角绑定数据
      let dataJson: any = {};
      try {
        dataJson = response.dataJson ? JSON.parse(response.dataJson) : {};
        console.log('[视角切换] dataJson:', dataJson);
      } catch (error) {
        console.log('[视角切换] JSON解析失败:', error);
        return;
      }

      // 检查是否有视角绑定
      if (dataJson.views && dataJson.views[slideIndex]) {
        const view = dataJson.views[slideIndex];
        console.log(`[视角切换] 找到第${slideIndex + 1}张图片的视角绑定:`, view);

        const cameraController = CameraController.getInstance();

        if (cameraController && Array.isArray(view) && view.length === 6) {
          const position = { x: view[0], y: view[1], z: view[2] };
          const target = { x: view[3], y: view[4], z: view[5] };
          console.log('[视角切换] 应用视角 - 位置:', position, '目标:', target);

          // 开始移动相机，并在完成后显示确认信息
          cameraController.moveToPosition(position, target, 1000, () => {
            console.log('[视角切换] 相机移动完成，视角已切换');
            message.success(`已切换到第${slideIndex + 1}张图片的绑定视角`);
          });

          // 立即显示开始信息
          console.log(`[视角切换] 正在切换到第${slideIndex + 1}张图片的绑定视角...`);
        } else {
          console.log('[视角切换] 相机控制器或视角数据无效');
          if (!cameraController) {
            console.error('[视角切换] CameraController 实例为空');
          }
          if (!Array.isArray(view)) {
            console.error('[视角切换] 视角数据不是数组:', typeof view, view);
          }
          if (view.length !== 6) {
            console.error('[视角切换] 视角数据长度不正确:', view.length, '期望: 6');
          }
        }
      } else {
        console.log(`[视角切换] 第${slideIndex + 1}张图片没有视角绑定`);
      }
    } catch (error) {
      console.error('[视角切换] 应用视角绑定失败:', error);
    }
  };

  // 应用场景类型切换（PPT专用，不会关闭组件）
  const applySceneTypeChange = async (sceneType: 'interior' | 'exterior') => {
    try {
      const { ModelLoaderManager } = await import('../../lib/load/ModelLoaderManager');
      const modelLoader = ModelLoaderManager.getInstance();

      console.log(`[SimplePPTPlayer] PPT模式切换场景类型到: ${sceneType}`);

      if (sceneType === 'exterior') {
        // PPT模式下，仅加载外景模型，不切换场景类型，避免组件关闭
        await modelLoader?.loadExteriorModelOnly();
        console.log('[SimplePPTPlayer] 外景模型已加载，场景类型保持不变');
      } else if (sceneType === 'interior') {
        // 对于内景，我们需要切换到一个楼层
        // 使用全局状态中的当前楼层ID，或者默认使用第一个楼层
        const currentFloorId = globalThreeStore.currentFloorId;
        if (currentFloorId) {
          await modelLoader?.showFloor(currentFloorId);
        } else {
          // 如果没有当前楼层ID，使用buildingData中的第一个楼层
          const { buildingData } = await import('@/data/buildingData');
          const firstFloor = buildingData.floors?.[0];
          if (firstFloor) {
            await modelLoader?.showFloor(firstFloor.id);
            globalThreeStore.setCurrentFloorId(firstFloor.id);
          }
        }
        console.log('[SimplePPTPlayer] 内景模型已加载');
      }

      // 等待模型加载完成
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      console.error('[SimplePPTPlayer] 场景类型切换失败:', error);
      throw error;
    }
  };

  // 应用设备聚焦
  const applyDeviceFocus = async (deviceName: string) => {
    try {
      console.log(`[SimplePPTPlayer] 聚焦设备: ${deviceName}`);

      // 这里可以添加设备聚焦逻辑
      // 例如：查找设备并调整相机位置以更好地观察设备
      const { SceneManager } = await import('../../lib/SceneManager');
      const sceneManager = SceneManager.getInstance();

      if (sceneManager && sceneManager.scene) {
        // 在场景中查找设备
        let deviceObject: THREE.Object3D | null = null;
        sceneManager.scene.traverse((object) => {
          if (object.name.toLowerCase().includes(deviceName.toLowerCase())) {
            deviceObject = object;
          }
        });

        if (deviceObject) {
          console.log(`[SimplePPTPlayer] 找到设备对象: ${deviceObject.name}`);
          // 可以在这里添加设备高亮或其他视觉效果
        }
      }
    } catch (error) {
      console.error('[SimplePPTPlayer] 设备聚焦失败:', error);
      throw error;
    }
  };

  // 最简单的视角绑定功能
  const bindCurrentSlideView = async () => {
    if (!currentPPT.value) {
      message.error('请先选择一个PPT');
      return;
    }

    const cameraController = CameraController.getInstance();
    if (!cameraController) {
      message.error('无法获取相机控制器');
      return;
    }

    try {
      // 获取当前相机位置和目标
      const position = cameraController.getCameraPosition();
      const target = cameraController.currentTarget || { x: 0, y: 0, z: 0 };

      // 获取当前PPT数据
      const response = await apiCall(
        {
          url: `/pptView/${currentPPT.value.id}`,
          method: 'GET',
        },
        {
          silent: true,
          errorMessage: '获取PPT数据失败',
          showLoading: true,
        }
      );

      if (!response) return;

      // 创建极简的数据结构
      let dataJson: any = {};
      try {
        const existingData = response.dataJson ? JSON.parse(response.dataJson) : {};
        // 只保留必要的基本信息
        dataJson.name = existingData.name || '';
        dataJson.views = existingData.views || {};
      } catch (error) {
        dataJson = { views: {} };
      }

      // 添加视角绑定 - 用更紧凑的数组格式：[px,py,pz,tx,ty,tz]
      dataJson.views[currentSlide.value] = [
        Math.round(position.x * 100) / 100, // 相机位置
        Math.round(position.y * 100) / 100,
        Math.round(position.z * 100) / 100,
        Math.round(target.x * 100) / 100, // 目标位置
        Math.round(target.y * 100) / 100,
        Math.round(target.z * 100) / 100,
      ];

      // 保存到服务器
      const updateResponse = await apiCall(
        {
          url: '/pptView',
          method: 'PUT',
          data: {
            id: currentPPT.value.id,
            photoUrl: response.photoUrl,
            dataJson: JSON.stringify(dataJson),
            orderNum: currentPPT.value.orderNum,
          },
        },
        {
          silent: true,
          errorMessage: '保存视角绑定失败',
          showLoading: true,
        }
      );

      if (updateResponse !== null) {
        // 更新视角绑定状态缓存
        currentPPTViewBindings.value[currentSlide.value] = true;
        message.success(`第${currentSlide.value + 1}张图片视角绑定成功`);
      }
    } catch (error) {
      console.error('绑定视角失败:', error);
      message.error('绑定视角失败');
    }
  };

  // 监听自动播放间隔变化
  watch(autoPlayInterval, () => {
    if (isAutoPlaying.value) {
      startAutoPlay(); // 重新启动定时器
    }
  });

  // 监听图片数组变化
  watch(
    images,
    () => {
      updateTotalSlides();
    },
    { deep: true }
  );

  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowLeft':
      case 'PageUp':
        event.preventDefault();
        previousSlide();
        break;
      case 'ArrowRight':
      case 'PageDown':
        event.preventDefault();
        nextSlide();
        break;
      case ' ':
        event.preventDefault();
        toggleAutoPlay();
        break;
    }
  };

  // 生命周期
  onMounted(() => {
    initializePPT();
    jumpToSlide.value = currentSlide.value + 1;

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyDown);
  });

  onUnmounted(() => {
    stopAutoPlay();

    // 移除键盘事件监听
    document.removeEventListener('keydown', handleKeyDown);

    // 清理错误缓存
    errorMessageCache.value.clear();
  });
</script>

<style scoped>
  .simple-ppt-player {
    background: linear-gradient(135deg, #0a0f1c 0%, #1a2332 100%);
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 0.3vw;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(36, 108, 249, 0.3);
    border-radius: 0.15vw;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
</style>
